from flask import Flask, render_template, request, jsonify, send_file, url_for
from werkzeug.utils import secure_filename
from ultralytics import YOLO
import os
import threading
import time
import uuid
import json
import gc
import cv2
import base64
import numpy as np
from datetime import datetime
from PIL import Image
import io

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['OUTPUT_FOLDER'] = 'outputs'
app.config['MODEL_FOLDER'] = 'models'

# Create necessary directories
for folder in [app.config['UPLOAD_FOLDER'], app.config['OUTPUT_FOLDER'], app.config['MODEL_FOLDER']]:
    os.makedirs(folder, exist_ok=True)

# Global variables for tracking processing status
processing_status = {}
uploaded_files = {'model': None, 'videos': []}  # Changed to support multiple videos
processing_queue = []
queue_lock = threading.Lock()
max_concurrent_jobs = 1  # Reduced to 1 for better performance

class ProcessingTracker:
    def __init__(self):
        self.status = "idle"
        self.progress = 0
        self.message = ""
        self.output_file = None
        self.error = None
        self.start_time = None
        self.end_time = None
        # Advanced analytics
        self.total_frames = 0
        self.processed_frames = 0
        self.detections_count = 0
        self.avg_confidence = 0.0
        self.fps = 0.0
        self.current_frame_preview = None
        self.detection_history = []
        self.processing_speed = []
        self.memory_usage = []
        self.model_info = {}
        self.video_info = {}
        # Queue and ROI features
        self.queue_position = 0
        self.priority = 1  # 1=low, 2=normal, 3=high
        self.roi_coordinates = None  # [x1, y1, x2, y2]
        self.video_filename = ""
        self.model_filename = ""

def allowed_file(filename, file_type):
    """Check if file extension is allowed"""
    if file_type == 'model':
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in ['pt', 'onnx']
    elif file_type == 'video':
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv']
    return False

def get_video_info(video_path):
    """Extract comprehensive video information"""
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return {}

        info = {
            'total_frames': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'duration': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0,
            'codec': int(cap.get(cv2.CAP_PROP_FOURCC)),
            'file_size': os.path.getsize(video_path) / (1024 * 1024)  # MB
        }
        cap.release()
        return info
    except Exception as e:
        print(f"[DEBUG] Error getting video info: {e}")
        return {}

def frame_to_base64(frame):
    """Convert OpenCV frame to base64 for web display"""
    try:
        # Resize frame for preview (max 400px width)
        height, width = frame.shape[:2]
        if width > 400:
            scale = 400 / width
            new_width = 400
            new_height = int(height * scale)
            frame = cv2.resize(frame, (new_width, new_height))

        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Convert to PIL Image
        pil_image = Image.fromarray(frame_rgb)

        # Convert to base64
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=85)
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return f"data:image/jpeg;base64,{img_str}"
    except Exception as e:
        print(f"[DEBUG] Error converting frame to base64: {e}")
        return None

def get_system_memory_usage():
    """Get current system memory usage"""
    try:
        import psutil
        memory = psutil.virtual_memory()
        return {
            'total': memory.total / (1024**3),  # GB
            'available': memory.available / (1024**3),  # GB
            'percent': memory.percent,
            'used': memory.used / (1024**3)  # GB
        }
    except ImportError:
        return {'error': 'psutil not available'}

def calculate_optimal_processing_params():
    """Calculate optimal processing parameters - simplified for better performance"""
    try:
        import psutil
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)

        # Simplified parameters for better performance
        return {
            'imgsz': 640,  # Fixed size for consistency
            'chunk_size': 50,  # Moderate chunk size
            'max_concurrent': 1,  # Single job for stability
            'memory_available': available_gb
        }
    except:
        return {'imgsz': 640, 'chunk_size': 50, 'max_concurrent': 1, 'memory_available': 4}

def add_to_processing_queue(task_id, model_path, video_path, confidence, roi=None, priority=1):
    """Add a processing job to the queue"""
    global processing_queue, queue_lock

    with queue_lock:
        job = {
            'task_id': task_id,
            'model_path': model_path,
            'video_path': video_path,
            'confidence': confidence,
            'roi': roi,
            'priority': priority,
            'added_time': datetime.now(),
            'status': 'queued'
        }

        # Insert based on priority (higher priority first)
        inserted = False
        for i, existing_job in enumerate(processing_queue):
            if existing_job['priority'] < priority:
                processing_queue.insert(i, job)
                inserted = True
                break

        if not inserted:
            processing_queue.append(job)

        # Update queue positions
        update_queue_positions()

        print(f"[QUEUE] Added job {task_id} to queue at position {get_queue_position(task_id)}")

        # Start processing if slots available
        start_next_job_if_available()

def update_queue_positions():
    """Update queue positions for all jobs"""
    for i, job in enumerate(processing_queue):
        if job['task_id'] in processing_status:
            processing_status[job['task_id']].queue_position = i + 1

def get_queue_position(task_id):
    """Get the position of a task in the queue"""
    for i, job in enumerate(processing_queue):
        if job['task_id'] == task_id:
            return i + 1
    return 0

def get_active_jobs_count():
    """Count currently active processing jobs"""
    active_count = 0
    for tracker in processing_status.values():
        if tracker.status == 'processing':
            active_count += 1
    return active_count

def start_next_job_if_available():
    """Start the next job in queue if processing slots are available"""
    global processing_queue, queue_lock

    with queue_lock:
        active_jobs = get_active_jobs_count()
        optimal_params = calculate_optimal_processing_params()
        max_concurrent = optimal_params['max_concurrent']

        if active_jobs < max_concurrent and processing_queue:
            # Find next queued job
            for i, job in enumerate(processing_queue):
                if job['status'] == 'queued':
                    job['status'] = 'starting'
                    processing_queue.pop(i)

                    # Start processing in background thread
                    thread = threading.Thread(
                        target=process_video_background_enhanced,
                        args=(job['model_path'], job['video_path'], job['confidence'],
                              job['task_id'], job['roi'])
                    )
                    thread.daemon = True
                    thread.start()

                    print(f"[QUEUE] Started processing job {job['task_id']}")
                    break

def get_video_frame_count(video_path):
    """Get total frame count from video file"""
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return None

        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        cap.release()
        return frame_count if frame_count > 0 else None
    except Exception as e:
        print(f"[DEBUG] Error getting frame count: {e}")
        return None

def process_video_background_enhanced(model_path, video_path, confidence, task_id, roi=None):
    """Enhanced background function to process video with ROI and smart memory management"""
    global processing_status

    print(f"[DEBUG] Starting enhanced processing for task {task_id}")

    tracker = ProcessingTracker()
    tracker.status = "processing"
    tracker.start_time = datetime.now()
    tracker.message = "Initializing smart processing..."
    tracker.video_filename = os.path.basename(video_path)
    tracker.model_filename = os.path.basename(model_path)
    tracker.roi_coordinates = roi
    processing_status[task_id] = tracker

    try:
        # Get optimal processing parameters
        optimal_params = calculate_optimal_processing_params()
        print(f"[DEBUG] Optimal params: {optimal_params}")

        # Load model with memory optimization
        print(f"[DEBUG] Loading model from {model_path}")
        model = YOLO(model_path)
        tracker.model_info = {
            'model_path': os.path.basename(model_path),
            'model_size': os.path.getsize(model_path) / (1024 * 1024),  # MB
            'device': 'cpu',
            'imgsz': optimal_params['imgsz']
        }
        tracker.progress = 10
        tracker.message = "Model loaded. Analyzing video..."

        # Get video information
        tracker.video_info = get_video_info(video_path)
        tracker.total_frames = tracker.video_info.get('total_frames', 0)

        # Create output directory
        output_dir = os.path.join(app.config['OUTPUT_FOLDER'], task_id)
        os.makedirs(output_dir, exist_ok=True)

        tracker.progress = 20
        tracker.message = f"Processing {tracker.total_frames} frames with ROI..." if roi else f"Processing {tracker.total_frames} frames..."

        # Use YOLO processing with optional ROI
        print(f"[DEBUG] Starting YOLO prediction with ROI: {roi}")

        if roi:
            # Process with ROI using custom frame-by-frame processing
            process_video_with_roi_simple(model, video_path, output_dir, confidence, tracker, roi)
        else:
            # Use standard YOLO processing for better performance
            results = model.predict(
                source=video_path,
                save=True,
                conf=confidence,
                project=output_dir,
                name='detection_results',
                verbose=False,
                stream=True,
                imgsz=640,
                device='cpu'
            )

            # Process results with progress tracking
            frame_count = 0
            for result in results:
                frame_count += 1
                tracker.processed_frames = frame_count

                # Update progress every 25 frames
                if frame_count % 25 == 0:
                    if tracker.total_frames > 0:
                        progress = 20 + int((frame_count / tracker.total_frames) * 70)
                        tracker.progress = min(progress, 90)
                        tracker.message = f"Processing frame {frame_count}/{tracker.total_frames}..."
                        print(f"[DEBUG] Progress: {tracker.progress}% - Frame {frame_count}")

                    # Memory cleanup
                    gc.collect()

        tracker.progress = 90
        tracker.message = "Finalizing output..."

        # Find output file
        result_dir = os.path.join(output_dir, 'detection_results')
        if os.path.exists(result_dir):
            for file in os.listdir(result_dir):
                if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    tracker.output_file = os.path.join(result_dir, file)
                    break

        tracker.progress = 100
        tracker.status = "completed"
        tracker.message = "Processing completed successfully!"
        tracker.end_time = datetime.now()

        print(f"[DEBUG] Enhanced processing completed for task {task_id}")

    except Exception as e:
        print(f"[DEBUG] Error in enhanced processing: {str(e)}")
        tracker.status = "error"
        tracker.error = str(e)
        tracker.message = f"Error: {str(e)}"
        tracker.end_time = datetime.now()

    finally:
        # Clean up memory and start next job
        gc.collect()
        start_next_job_if_available()

def process_video_with_roi_simple(model, video_path, output_dir, confidence, tracker, roi):
    """Simple ROI processing for better performance"""

    # Open video capture
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise Exception("Could not open video file")

    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    output_path = os.path.join(output_dir, 'detection_results')
    os.makedirs(output_path, exist_ok=True)
    output_file = os.path.join(output_path, f'processed_{os.path.basename(video_path)}')
    out = cv2.VideoWriter(output_file, fourcc, fps, (width, height))

    frame_count = 0
    x1, y1, x2, y2 = roi

    # Ensure ROI coordinates are within frame bounds
    x1 = max(0, min(int(x1), width))
    y1 = max(0, min(int(y1), height))
    x2 = max(x1, min(int(x2), width))
    y2 = max(y1, min(int(y2), height))

    print(f"[DEBUG] Processing with ROI: ({x1}, {y1}) to ({x2}, {y2})")

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frame_count += 1

            # Extract ROI
            roi_frame = frame[y1:y2, x1:x2]

            if roi_frame.size > 0:
                # Process only ROI
                results = model.predict(
                    roi_frame,
                    conf=confidence,
                    imgsz=640,
                    verbose=False,
                    device='cpu'
                )

                # Draw detections on original frame
                if results and len(results) > 0:
                    for result in results:
                        if result.boxes is not None:
                            for box in result.boxes:
                                # Adjust coordinates back to original frame
                                bx1, by1, bx2, by2 = box.xyxy[0].cpu().numpy()
                                bx1 += x1
                                by1 += y1
                                bx2 += x1
                                by2 += y1

                                # Draw bounding box
                                cv2.rectangle(frame, (int(bx1), int(by1)),
                                            (int(bx2), int(by2)), (0, 255, 0), 2)

                                # Draw confidence
                                conf_score = box.conf[0].cpu().numpy()
                                cv2.putText(frame, f'{conf_score:.2f}',
                                          (int(bx1), int(by1)-10),
                                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                                tracker.detections_count += 1

            # Draw ROI rectangle
            cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 0, 0), 2)
            cv2.putText(frame, 'ROI', (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

            # Write frame
            out.write(frame)
            tracker.processed_frames = frame_count

            # Update progress every 25 frames
            if frame_count % 25 == 0:
                progress = 20 + int((frame_count / total_frames) * 70)
                tracker.progress = min(progress, 90)
                tracker.message = f"Processing ROI frame {frame_count}/{total_frames}. Detections: {tracker.detections_count}"

                # Memory cleanup
                gc.collect()

                print(f"[DEBUG] ROI Progress: {tracker.progress}% - Frame {frame_count}/{total_frames}")

    finally:
        cap.release()
        out.release()
        cv2.destroyAllWindows()

def process_video_with_roi_and_chunking(model, video_path, output_dir, confidence, tracker, optimal_params, roi=None):
    """Process video with ROI support and smart chunking for memory management"""

    # Open video capture
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise Exception("Could not open video file")

    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    output_path = os.path.join(output_dir, 'detection_results')
    os.makedirs(output_path, exist_ok=True)
    output_file = os.path.join(output_path, f'processed_{os.path.basename(video_path)}')
    out = cv2.VideoWriter(output_file, fourcc, fps, (width, height))

    frame_count = 0
    chunk_size = optimal_params['chunk_size']

    print(f"[DEBUG] Processing with chunk size: {chunk_size}, ROI: {roi}")

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frame_count += 1

            # Apply ROI if specified
            if roi:
                x1, y1, x2, y2 = roi
                # Ensure coordinates are within frame bounds
                x1 = max(0, min(x1, width))
                y1 = max(0, min(y1, height))
                x2 = max(x1, min(x2, width))
                y2 = max(y1, min(y2, height))

                # Create ROI mask
                roi_frame = frame[y1:y2, x1:x2]
                if roi_frame.size > 0:
                    # Process only ROI
                    results = model.predict(
                        roi_frame,
                        conf=confidence,
                        imgsz=optimal_params['imgsz'],
                        verbose=False,
                        device='cpu'
                    )

                    # Draw detections on original frame
                    if results and len(results) > 0:
                        for result in results:
                            if result.boxes is not None:
                                for box in result.boxes:
                                    # Adjust coordinates back to original frame
                                    x1_det, y1_det, x2_det, y2_det = box.xyxy[0].cpu().numpy()
                                    x1_det += x1
                                    y1_det += y1
                                    x2_det += x1
                                    y2_det += y1

                                    # Draw bounding box
                                    cv2.rectangle(frame, (int(x1_det), int(y1_det)),
                                                (int(x2_det), int(y2_det)), (0, 255, 0), 2)

                                    # Draw confidence
                                    conf_score = box.conf[0].cpu().numpy()
                                    cv2.putText(frame, f'{conf_score:.2f}',
                                              (int(x1_det), int(y1_det)-10),
                                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                                    tracker.detections_count += 1

                # Draw ROI rectangle
                cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 0, 0), 2)
                cv2.putText(frame, 'ROI', (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

            else:
                # Process entire frame
                results = model.predict(
                    frame,
                    conf=confidence,
                    imgsz=optimal_params['imgsz'],
                    verbose=False,
                    device='cpu'
                )

                # Draw detections
                if results and len(results) > 0:
                    for result in results:
                        if result.boxes is not None:
                            for box in result.boxes:
                                x1_det, y1_det, x2_det, y2_det = box.xyxy[0].cpu().numpy()
                                cv2.rectangle(frame, (int(x1_det), int(y1_det)),
                                            (int(x2_det), int(y2_det)), (0, 255, 0), 2)

                                conf_score = box.conf[0].cpu().numpy()
                                cv2.putText(frame, f'{conf_score:.2f}',
                                          (int(x1_det), int(y1_det)-10),
                                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                                tracker.detections_count += 1

            # Write frame
            out.write(frame)
            tracker.processed_frames = frame_count

            # Update progress every chunk_size frames
            if frame_count % chunk_size == 0:
                progress = 20 + int((frame_count / total_frames) * 70)
                tracker.progress = min(progress, 90)
                tracker.message = f"Processed {frame_count}/{total_frames} frames. Detections: {tracker.detections_count}"

                # Memory cleanup every chunk
                gc.collect()

                print(f"[DEBUG] Processed {frame_count}/{total_frames} frames, {tracker.detections_count} detections")

    finally:
        cap.release()
        out.release()
        cv2.destroyAllWindows()

def process_video_background(model_path, video_path, confidence, task_id):
    """Background function to process video with YOLO model"""
    global processing_status

    print(f"[DEBUG] Starting background processing for task {task_id}")

    tracker = ProcessingTracker()
    tracker.status = "processing"
    tracker.start_time = datetime.now()
    tracker.message = "Loading model..."
    processing_status[task_id] = tracker

    try:
        print(f"[DEBUG] Loading model from {model_path}")
        # Load model and extract model info
        model = YOLO(model_path)
        tracker.model_info = {
            'model_path': os.path.basename(model_path),
            'model_size': os.path.getsize(model_path) / (1024 * 1024),  # MB
            'device': 'cpu'
        }
        tracker.progress = 10
        tracker.message = "Model loaded. Analyzing video..."
        print(f"[DEBUG] Model loaded successfully")

        # Get comprehensive video information
        tracker.video_info = get_video_info(video_path)
        tracker.total_frames = tracker.video_info.get('total_frames', 0)

        # Create output directory for this task
        output_dir = os.path.join(app.config['OUTPUT_FOLDER'], task_id)
        os.makedirs(output_dir, exist_ok=True)
        print(f"[DEBUG] Output directory created: {output_dir}")
        print(f"[DEBUG] Video info: {tracker.video_info}")

        tracker.progress = 20
        tracker.message = f"Processing {tracker.total_frames} frames..."

        print(f"[DEBUG] Starting video prediction on {video_path}")
        # Run prediction with memory optimization and progress tracking
        results = model.predict(
            source=video_path,
            save=True,
            conf=confidence,
            project=output_dir,
            name='detection_results',
            verbose=False,  # Reduce console output
            stream=True,    # Use streaming to reduce memory usage
            imgsz=640,      # Reduce image size if needed
            half=False,     # Disable half precision as it may cause issues on CPU
            device='cpu'    # Force CPU to avoid GPU memory issues
        )

        # Process results in streaming mode to manage memory
        frame_count = 0
        total_frames = None

        print(f"[DEBUG] Processing video frames in streaming mode...")
        for result in results:
            frame_count += 1

            # Try to get total frames from the first result
            if total_frames is None and hasattr(result, 'orig_shape'):
                # Estimate total frames (this is approximate)
                total_frames = 1830  # You can adjust this or get it from video metadata

            # Update progress every 50 frames
            if frame_count % 50 == 0:
                if total_frames:
                    progress = 20 + int((frame_count / total_frames) * 70)  # 20% to 90%
                    tracker.progress = min(progress, 90)
                    tracker.message = f"Processing frame {frame_count}/{total_frames}..."
                    print(f"[DEBUG] Processed {frame_count} frames, progress: {tracker.progress}%")
                else:
                    tracker.message = f"Processing frame {frame_count}..."
                    print(f"[DEBUG] Processed {frame_count} frames")

        print(f"[DEBUG] Video prediction completed - processed {frame_count} frames")
        tracker.progress = 90
        tracker.message = "Finalizing output..."

        # Find the output video file
        result_dir = os.path.join(output_dir, 'detection_results')
        print(f"[DEBUG] Looking for output in {result_dir}")

        if os.path.exists(result_dir):
            files = os.listdir(result_dir)
            print(f"[DEBUG] Files in result directory: {files}")
            for file in files:
                if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    tracker.output_file = os.path.join(result_dir, file)
                    print(f"[DEBUG] Found output video: {tracker.output_file}")
                    break

        tracker.progress = 100
        tracker.status = "completed"
        tracker.message = "Video processing completed successfully!"
        tracker.end_time = datetime.now()
        print(f"[DEBUG] Processing completed successfully for task {task_id}")

    except Exception as e:
        print(f"[DEBUG] Error in processing: {str(e)}")
        tracker.status = "error"
        tracker.error = str(e)
        tracker.message = f"Error: {str(e)}"
        tracker.end_time = datetime.now()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/upload_model', methods=['POST'])
def upload_model():
    """Handle model file upload"""
    if 'model' not in request.files:
        return jsonify({'success': False, 'message': 'No model file selected'})

    file = request.files['model']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No model file selected'})

    if file and allowed_file(file.filename, 'model'):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['MODEL_FOLDER'], filename)
        file.save(filepath)
        uploaded_files['model'] = filepath
        return jsonify({'success': True, 'message': f'Model uploaded: {filename}', 'filename': filename})

    return jsonify({'success': False, 'message': 'Invalid model file format. Please upload .pt or .onnx files.'})

@app.route('/upload_videos', methods=['POST'])
def upload_videos():
    """Handle single or multiple video file uploads"""
    try:
        print(f"[DEBUG] Upload videos endpoint called")
        print(f"[DEBUG] Request files: {list(request.files.keys())}")

        files = request.files.getlist('videos')
        print(f"[DEBUG] Found {len(files)} files")

        if not files or all(f.filename == '' for f in files):
            return jsonify({'success': False, 'message': 'No video files selected'})

        uploaded_videos = []
        errors = []

        for file in files:
            print(f"[DEBUG] Processing file: {file.filename}")
            if file.filename == '':
                continue

            if file and allowed_file(file.filename, 'video'):
                try:
                    filename = secure_filename(file.filename)
                    # Add timestamp to avoid conflicts
                    timestamp = int(time.time())
                    unique_filename = f"{timestamp}_{filename}"
                    filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                    file.save(filepath)

                    uploaded_videos.append({
                        'filename': unique_filename,
                        'original_name': file.filename,
                        'path': filepath,
                        'size': os.path.getsize(filepath)
                    })
                    print(f"[DEBUG] Successfully uploaded: {unique_filename}")
                except Exception as e:
                    print(f"[DEBUG] Error uploading {file.filename}: {str(e)}")
                    errors.append(f"Error uploading {file.filename}: {str(e)}")
            else:
                errors.append(f"Invalid file format: {file.filename}")

        if uploaded_videos:
            uploaded_files['videos'] = uploaded_videos
            print(f"[DEBUG] Upload successful: {len(uploaded_videos)} videos")
            return jsonify({
                'success': True,
                'message': f'Uploaded {len(uploaded_videos)} video(s)',
                'videos': uploaded_videos,
                'errors': errors
            })

        return jsonify({'success': False, 'message': 'No valid video files uploaded', 'errors': errors})

    except Exception as e:
        print(f"[DEBUG] Upload endpoint error: {str(e)}")
        return jsonify({'success': False, 'message': f'Upload error: {str(e)}'})

@app.route('/start_processing', methods=['POST'])
def start_processing():
    """Start video processing with optional ROI"""
    if not uploaded_files['model'] or not uploaded_files['videos']:
        return jsonify({'success': False, 'message': 'Please upload both model and video files'})

    data = request.json or {}
    confidence = float(data.get('confidence', 0.25))
    roi_selections = data.get('roi_selections', {})  # Dictionary of video_filename -> ROI coordinates

    task_ids = []

    # Process all uploaded videos
    for video_info in uploaded_files['videos']:
        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Get ROI for this specific video
        roi = roi_selections.get(video_info['filename'])

        # Create tracker
        tracker = ProcessingTracker()
        tracker.status = "queued"
        tracker.message = "Added to processing queue..."
        tracker.video_filename = video_info['original_name']
        tracker.model_filename = os.path.basename(uploaded_files['model'])
        tracker.roi_coordinates = roi
        processing_status[task_id] = tracker

        # Add to processing queue
        add_to_processing_queue(
            task_id, uploaded_files['model'], video_info['path'],
            confidence, roi, 2  # Normal priority with ROI
        )

        task_ids.append({
            'task_id': task_id,
            'video_name': video_info['original_name'],
            'has_roi': roi is not None
        })

    return jsonify({
        'success': True,
        'task_ids': task_ids,
        'message': f'Added {len(task_ids)} video(s) to processing queue'
    })

@app.route('/status/<task_id>')
def get_status(task_id):
    """Get processing status"""
    print(f"[DEBUG] Status request for task {task_id}")
    print(f"[DEBUG] Available tasks: {list(processing_status.keys())}")

    if task_id not in processing_status:
        print(f"[DEBUG] Task {task_id} not found in processing_status")
        return jsonify({'status': 'not_found', 'message': 'Task not found. The server may have restarted.'})

    tracker = processing_status[task_id]
    print(f"[DEBUG] Task {task_id} status: {tracker.status}, progress: {tracker.progress}")

    response = {
        'status': tracker.status,
        'progress': tracker.progress,
        'message': tracker.message
    }

    if tracker.status == 'completed' and tracker.output_file:
        response['download_url'] = url_for('download_result', task_id=task_id)
        response['output_filename'] = os.path.basename(tracker.output_file)

    if tracker.status == 'error':
        response['error'] = tracker.error

    if tracker.start_time:
        response['start_time'] = tracker.start_time.strftime('%Y-%m-%d %H:%M:%S')

    if tracker.end_time:
        response['end_time'] = tracker.end_time.strftime('%Y-%m-%d %H:%M:%S')
        if tracker.start_time:
            duration = tracker.end_time - tracker.start_time
            response['duration'] = str(duration).split('.')[0]  # Remove microseconds

    return jsonify(response)

@app.route('/download/<task_id>')
def download_result(task_id):
    """Download processed video"""
    if task_id not in processing_status:
        return "Task not found", 404

    tracker = processing_status[task_id]
    if tracker.status != 'completed' or not tracker.output_file:
        return "File not ready", 404

    if not os.path.exists(tracker.output_file):
        return "File not found", 404

    return send_file(tracker.output_file, as_attachment=True)

@app.route('/clear_files', methods=['POST'])
def clear_files():
    """Clear uploaded files"""
    global uploaded_files
    uploaded_files = {'model': None, 'videos': []}
    return jsonify({'success': True, 'message': 'Files cleared'})

@app.route('/queue_status')
def queue_status():
    """Get current processing queue status"""
    global processing_queue, queue_lock

    with queue_lock:
        queue_info = []
        for i, job in enumerate(processing_queue):
            if job['task_id'] in processing_status:
                tracker = processing_status[job['task_id']]
                queue_info.append({
                    'position': i + 1,
                    'task_id': job['task_id'],
                    'video_filename': tracker.video_filename,
                    'priority': job['priority'],
                    'status': job['status'],
                    'added_time': job['added_time'].strftime('%H:%M:%S'),
                    'has_roi': tracker.roi_coordinates is not None
                })

        active_jobs = []
        for task_id, tracker in processing_status.items():
            if tracker.status == 'processing':
                active_jobs.append({
                    'task_id': task_id,
                    'video_filename': tracker.video_filename,
                    'progress': tracker.progress,
                    'message': tracker.message,
                    'detections': tracker.detections_count
                })

    optimal_params = calculate_optimal_processing_params()

    return jsonify({
        'success': True,
        'queue': queue_info,
        'active_jobs': active_jobs,
        'queue_length': len(processing_queue),
        'active_count': len(active_jobs),
        'max_concurrent': optimal_params['max_concurrent'],
        'memory_available': optimal_params['memory_available']
    })





@app.route('/video_preview/<filename>')
def video_preview(filename):
    """Serve video preview"""
    try:
        video_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if os.path.exists(video_path):
            return send_file(video_path)
        return "Video not found", 404
    except Exception as e:
        return f"Error: {str(e)}", 500

@app.route('/get_video_info', methods=['POST'])
def get_video_info_endpoint():
    """Get detailed video information"""
    if not uploaded_files['videos']:
        return jsonify({'success': False, 'message': 'No video uploaded'})

    try:
        video_info = get_video_info(uploaded_files['videos'][0]['path'])
        return jsonify({
            'success': True,
            'info': video_info,
            'filename': uploaded_files['videos'][0]['original_name']
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error analyzing video: {str(e)}'})

@app.route('/get_first_frame/<video_filename>')
def get_first_frame(video_filename):
    """Extract and return the first frame of a video as base64 image"""
    try:
        # Find the video file
        video_path = None
        for video in uploaded_files['videos']:
            if video['filename'] == video_filename:
                video_path = video['path']
                break

        if not video_path or not os.path.exists(video_path):
            return jsonify({'success': False, 'message': 'Video file not found'})

        # Extract first frame
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return jsonify({'success': False, 'message': 'Could not open video file'})

        ret, frame = cap.read()
        cap.release()

        if not ret:
            return jsonify({'success': False, 'message': 'Could not read first frame'})

        # Convert frame to base64
        frame_base64 = frame_to_base64(frame)

        if frame_base64:
            return jsonify({
                'success': True,
                'frame_data': frame_base64,
                'width': frame.shape[1],
                'height': frame.shape[0]
            })
        else:
            return jsonify({'success': False, 'message': 'Could not convert frame to base64'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error extracting first frame: {str(e)}'})

def frame_to_base64(frame):
    """Convert OpenCV frame to base64 for web display"""
    try:
        # Resize frame for preview (max 600px width for ROI selection)
        height, width = frame.shape[:2]
        if width > 600:
            scale = 600 / width
            new_width = 600
            new_height = int(height * scale)
            frame = cv2.resize(frame, (new_width, new_height))

        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Encode as JPEG
        _, buffer = cv2.imencode('.jpg', cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR))
        img_str = base64.b64encode(buffer).decode()

        return f"data:image/jpeg;base64,{img_str}"
    except Exception as e:
        print(f"[DEBUG] Error converting frame to base64: {e}")
        return None

@app.route('/processing_history')
def processing_history():
    """Get processing history and statistics"""
    history = []
    for task_id, tracker in processing_status.items():
        if tracker.status in ['completed', 'error']:
            history.append({
                'task_id': task_id,
                'status': tracker.status,
                'start_time': tracker.start_time.strftime('%Y-%m-%d %H:%M:%S') if tracker.start_time else None,
                'end_time': tracker.end_time.strftime('%Y-%m-%d %H:%M:%S') if tracker.end_time else None,
                'duration': str(tracker.end_time - tracker.start_time).split('.')[0] if tracker.start_time and tracker.end_time else None,
                'output_file': os.path.basename(tracker.output_file) if tracker.output_file else None
            })

    return jsonify({
        'success': True,
        'history': history,
        'total_processed': len([h for h in history if h['status'] == 'completed']),
        'total_errors': len([h for h in history if h['status'] == 'error'])
    })

@app.route('/system_info')
def system_info():
    """Get system information"""
    try:
        import psutil
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        return jsonify({
            'success': True,
            'cpu_percent': cpu_percent,
            'memory': {
                'total': round(memory.total / (1024**3), 2),
                'available': round(memory.available / (1024**3), 2),
                'percent': memory.percent,
                'used': round(memory.used / (1024**3), 2)
            },
            'disk': {
                'total': round(disk.total / (1024**3), 2),
                'free': round(disk.free / (1024**3), 2),
                'percent': round((disk.used / disk.total) * 100, 1)
            }
        })
    except ImportError:
        return jsonify({
            'success': False,
            'message': 'System monitoring not available (psutil not installed)'
        })

if __name__ == '__main__':
    print("Starting YOLO Video Detection Web GUI...")
    print("Open your browser and go to: http://localhost:5000")
    # Disable debug mode to prevent auto-restart which kills background threads
    app.run(debug=False, host='0.0.0.0', port=5000)
